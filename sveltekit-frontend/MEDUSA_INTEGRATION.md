# Medusa Integration Guide

This document explains how the SvelteKit frontend integrates with your Medusa backend and how to configure it properly.

## Overview

The SvelteKit frontend has been updated to use real data from your Medusa backend instead of mock data. The integration includes:

- **Products API**: List, search, and retrieve products
- **Regions API**: Manage regions and country-specific data
- **Real-time data**: All product information comes directly from Medusa
- **Error handling**: Graceful fallbacks when API calls fail
- **Caching**: Intelligent caching to improve performance

## Configuration

### 1. Environment Variables

Copy the example environment file and configure it:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your Medusa backend details:

```env
# Medusa Backend Configuration
PUBLIC_MEDUSA_BACKEND_URL=http://localhost:9000
PUBLIC_MEDUSA_PUBLISHABLE_KEY=your_publishable_key_here

# Private environment variables (server-side only)
MEDUSA_BACKEND_URL=http://localhost:9000
MEDUSA_PUBLISHABLE_KEY=your_publishable_key_here
```

### 2. Publishable Key

To get your publishable key from Medusa:

1. Access your Medusa Admin dashboard
2. Go to Settings > Publishable API Keys
3. Create a new publishable key or copy an existing one
4. Add it to your environment variables

### 3. CORS Configuration

Make sure your Medusa backend allows requests from your SvelteKit frontend. In your Medusa backend's `medusa-config.js`:

```javascript
module.exports = {
  projectConfig: {
    // ... other config
    store_cors: "http://localhost:8000,http://localhost:5173", // Add your SvelteKit dev server
    admin_cors: "http://localhost:7001,http://localhost:7000",
  },
  // ... rest of config
}
```

## API Structure

### Products API (`src/lib/api/products.ts`)

- `listProducts(regionId, filters, pagination)` - List products with filtering and pagination
- `getProduct(id, regionId)` - Get a single product by ID
- `getProductByHandle(handle, regionId)` - Get a product by its handle/slug
- `searchProducts(query, regionId, pagination)` - Search products
- `getFeaturedProducts(regionId, limit)` - Get featured products

### Regions API (`src/lib/api/regions.ts`)

- `listRegions()` - Get all available regions
- `getRegion(countryCode)` - Get region by country code
- `getDefaultRegion()` - Get the default region (US or first available)
- `getCountryCodes()` - Get all supported country codes

### Medusa API (`src/lib/api/medusa.ts`)

Low-level API wrapper that handles:
- SDK initialization
- Error handling
- Request/response transformation
- Caching

## Testing the Integration

### 1. Test Page

Visit `/test-api` in your browser to run integration tests:

```
http://localhost:8000/test-api
```

This page will:
- Test connection to your Medusa backend
- Verify regions are configured
- Check if products are available
- Display detailed logs for troubleshooting

### 2. Manual Testing

You can also test the APIs programmatically:

```javascript
import { testMedusaConnection } from '$lib/test-medusa'

// Run full test suite
const success = await testMedusaConnection()
console.log('Integration working:', success)
```

## Data Flow

1. **Page Load**: Components call the appropriate API methods
2. **Region Detection**: System determines the user's region (defaults to US)
3. **Data Fetching**: Real product data is fetched from Medusa
4. **Transformation**: Medusa data is transformed to match frontend types
5. **Rendering**: Components display the real data
6. **Error Handling**: Graceful fallbacks if API calls fail

## Troubleshooting

### Common Issues

1. **"Failed to initialize Medusa SDK"**
   - Check that `@medusajs/js-sdk` is installed
   - Verify environment variables are set correctly

2. **"No regions found"**
   - Ensure your Medusa backend has regions configured
   - Check that the backend is running and accessible

3. **CORS errors**
   - Update your Medusa backend's CORS configuration
   - Make sure the frontend URL is whitelisted

4. **"Product not found" errors**
   - Verify your Medusa backend has products
   - Check that products are published and have variants

### Debug Steps

1. Check the browser console for errors
2. Visit `/test-api` to run diagnostics
3. Verify your Medusa backend is running: `http://localhost:9000/health`
4. Test the API directly: `http://localhost:9000/store/regions`

## Migration from Mock Data

The following changes were made to transition from mock data:

1. **Updated `ProductsAPI`**: Now calls real Medusa endpoints
2. **Added `MedusaAPI`**: Low-level API wrapper
3. **Enhanced `RegionsAPI`**: Real region management
4. **Environment configuration**: Added proper env var handling
5. **Error handling**: Improved error handling and fallbacks

## Performance Considerations

- **Caching**: Regions are cached for 1 hour to reduce API calls
- **Pagination**: Products are loaded in pages to improve performance
- **Error boundaries**: Failed API calls don't break the entire page
- **Loading states**: Proper loading indicators for better UX

## Next Steps

After setting up the integration:

1. Configure your Medusa backend with real product data
2. Set up proper regions for your target markets
3. Configure payment providers in Medusa
4. Set up shipping options and tax rates
5. Test the complete checkout flow

For more information, see the [Medusa documentation](https://docs.medusajs.com/).
