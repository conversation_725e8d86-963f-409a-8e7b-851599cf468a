# Jani Vapes - SvelteKit Frontend

A modern e-commerce frontend built with SvelteKit and integrated with Medusa v2 backend.

## Features

- 🛍️ **Product Catalog**: Browse and search products with filtering
- 🛒 **Shopping Cart**: Add to cart functionality with quantity management
- 👤 **User Authentication**: Customer registration and login
- 📱 **Responsive Design**: Mobile-first design with Tailwind CSS
- ⚡ **Fast Performance**: Built with SvelteKit for optimal performance
- 🔄 **Real-time Updates**: Reactive state management with Svelte stores

## Tech Stack

- **Framework**: SvelteKit
- **Styling**: Tailwind CSS
- **Backend Integration**: Medusa JS SDK v2
- **State Management**: Svelte stores
- **TypeScript**: Full type safety

## Prerequisites

- Node.js 18+
- npm or yarn
- Medusa backend running on port 9000

## Getting Started

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

   The application will be available at `http://localhost:8000`

## Key Features

### ProductCard Component
- Displays product information with "Add to Cart" functionality
- Prevents adding the same product multiple times (as per user preference)
- Responsive design with hover effects

### Cart Management
- Real-time cart updates using Svelte stores
- Quantity management and item removal
- Persistent cart state across sessions

### API Integration
- Products: Fetch, search, and filter
- Cart: Create, update, and manage
- Customer: Authentication and profiles
- Regions: Multi-region support

## Development Commands

- `npm run dev` - Start development server on port 8000
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run check` - Run TypeScript checks

## Backend Integration

This frontend works with the Medusa v2 backend in `../backend`. Ensure the backend runs on port 9000 with CORS: `STORE_CORS=http://localhost:8000`
