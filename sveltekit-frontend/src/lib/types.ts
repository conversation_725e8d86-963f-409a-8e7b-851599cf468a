import type { HttpTypes } from "@medusajs/types"

// Re-export commonly used types from Medusa
export type Product = HttpTypes.StoreProduct
export type ProductVariant = HttpTypes.StoreProductVariant
export type Cart = HttpTypes.StoreCart
export type CartLineItem = HttpTypes.StoreCartLineItem
export type Customer = HttpTypes.StoreCustomer
export type Region = HttpTypes.StoreRegion
export type Order = HttpTypes.StoreOrder
export type Collection = HttpTypes.StoreCollection
export type ProductCategory = HttpTypes.StoreProductCategory

// Custom application types
export interface CartState {
  cart: Cart | null
  isLoading: boolean
  error: string | null
}

export interface CustomerState {
  customer: Customer | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export interface ProductFilters {
  category_id?: string[]
  collection_id?: string[]
  price_min?: number
  price_max?: number
  tags?: string[]
  q?: string
}

export interface PaginationParams {
  limit?: number
  offset?: number
}

export interface ProductListResponse {
  products: Product[]
  count: number
  nextPage: number | null
}

export interface AddToCartParams {
  variantId: string
  quantity: number
}

export interface UpdateCartItemParams {
  lineId: string
  quantity: number
}

// UI Component Props
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  type?: 'button' | 'submit' | 'reset'
  class?: string
}

export interface InputProps {
  type?: string
  placeholder?: string
  value?: string
  disabled?: boolean
  required?: boolean
  class?: string
  error?: string
}

export interface ModalProps {
  open: boolean
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
}

// Navigation types
export interface NavItem {
  label: string
  href: string
  children?: NavItem[]
}

// Error handling
export interface ApiError {
  message: string
  code?: string
  details?: any
}
