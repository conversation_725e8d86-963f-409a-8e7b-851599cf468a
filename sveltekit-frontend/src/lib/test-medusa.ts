// Test script to verify Medusa API integration
import { MedusaAPI } from './api/medusa'
import { RegionsAPI } from './api/regions'
import { ProductsAPI } from './api/products'

export async function testMedusaConnection() {
  console.log('🧪 Testing Medusa API connection...')
  
  try {
    // Test 1: List regions
    console.log('📍 Testing regions API...')
    const regions = await RegionsAPI.listRegions()
    console.log(`✅ Found ${regions.length} regions:`, regions.map(r => r.name || r.id))
    
    if (regions.length === 0) {
      console.warn('⚠️ No regions found. Make sure your Medusa backend has regions configured.')
      return false
    }
    
    // Test 2: Get default region
    console.log('🌍 Testing default region...')
    const defaultRegion = await RegionsAPI.getDefaultRegion()
    console.log('✅ Default region:', defaultRegion?.name || defaultRegion?.id)
    
    if (!defaultRegion) {
      console.error('❌ No default region found')
      return false
    }
    
    // Test 3: List products
    console.log('📦 Testing products API...')
    const productsResponse = await ProductsAPI.listProducts(defaultRegion.id, {}, { limit: 5 })
    console.log(`✅ Found ${productsResponse.count} total products, showing ${productsResponse.products.length}:`)
    productsResponse.products.forEach(p => console.log(`  - ${p.title} (${p.id})`))
    
    // Test 4: Search products (if any exist)
    if (productsResponse.products.length > 0) {
      console.log('🔍 Testing product search...')
      const searchResponse = await ProductsAPI.searchProducts('', defaultRegion.id, { limit: 3 })
      console.log(`✅ Search returned ${searchResponse.response.products.length} products`)
    }
    
    console.log('🎉 All tests passed! Medusa API integration is working.')
    return true
    
  } catch (error) {
    console.error('❌ Medusa API test failed:', error)
    console.error('💡 Make sure:')
    console.error('  1. Medusa backend is running on http://localhost:9000')
    console.error('  2. Environment variables are set correctly')
    console.error('  3. Publishable key is configured (if required)')
    return false
  }
}

// Helper function to test individual components
export async function testRegionsOnly() {
  try {
    const regions = await RegionsAPI.listRegions()
    console.log('Regions test result:', regions)
    return regions
  } catch (error) {
    console.error('Regions test failed:', error)
    throw error
  }
}

export async function testProductsOnly(regionId: string) {
  try {
    const products = await ProductsAPI.listProducts(regionId, {}, { limit: 5 })
    console.log('Products test result:', products)
    return products
  } catch (error) {
    console.error('Products test failed:', error)
    throw error
  }
}
