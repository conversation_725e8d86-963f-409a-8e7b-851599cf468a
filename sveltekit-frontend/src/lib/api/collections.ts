import { sdk } from '$lib/config'
import type { Collection } from '$lib/types'

export class CollectionsAPI {
  // Get all collections
  static async listCollections(): Promise<Collection[]> {
    try {
      const response = await sdk.store.collection.list()
      return response.collections
    } catch (error) {
      console.error('Error fetching collections:', error)
      throw error
    }
  }

  // Get collection by ID
  static async getCollection(id: string): Promise<Collection> {
    try {
      const response = await sdk.store.collection.retrieve(id)
      return response.collection
    } catch (error) {
      console.error('Error fetching collection:', error)
      throw error
    }
  }
}
