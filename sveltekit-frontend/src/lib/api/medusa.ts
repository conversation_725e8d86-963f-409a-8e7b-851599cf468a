import { browser } from '$app/environment'
import { initializeSDK, getSDK } from '$lib/config'

// Error handling utility
export function medusaError(error: any): never {
  console.error('Medusa API Error:', error)

  if (error?.response?.data?.message) {
    throw new Error(error.response.data.message)
  }

  if (error?.message) {
    throw new Error(error.message)
  }

  throw new Error('An unexpected error occurred')
}

// Cache options for different resources
export function getCacheOptions(resource: string) {
  return {
    revalidate: 3600, // 1 hour
    tags: [resource],
  }
}

// Base Medusa API class
export class MedusaAPI {
  private static async ensureSDK() {
    let sdk = getSDK()
    if (!sdk) {
      sdk = await initializeSDK()
    }
    if (!sdk) {
      throw new Error('Failed to initialize Medusa SDK')
    }
    return sdk
  }

  // Generic fetch method for Medusa API
  protected static async fetch<T>(
    endpoint: string,
    options: {
      method?: string
      query?: Record<string, any>
      body?: any
      headers?: Record<string, string>
      cache?: RequestCache
    } = {}
  ): Promise<T> {
    const sdk = await this.ensureSDK()

    const {
      method = 'GET',
      query = {},
      body,
      headers = {},
      cache = 'default'
    } = options

    try {
      const response = await sdk.client.fetch<T>(endpoint, {
        method,
        query,
        body,
        headers,
        cache,
      })

      return response
    } catch (error) {
      medusaError(error)
    }
  }

  // List regions
  static async listRegions(): Promise<any[]> {
    try {
      const response = await this.fetch<{ regions: any[] }>('/store/regions', {
        cache: 'force-cache'
      })
      return response.regions
    } catch (error) {
      console.error('Error fetching regions:', error)
      return []
    }
  }

  // Get region by ID
  static async retrieveRegion(id: string): Promise<any | null> {
    try {
      const response = await this.fetch<{ region: any }>(`/store/regions/${id}`, {
        cache: 'force-cache'
      })
      return response.region
    } catch (error) {
      console.error('Error fetching region:', error)
      return null
    }
  }

  // Get region by country code
  static async getRegion(countryCode: string): Promise<any | null> {
    try {
      const regions = await this.listRegions()

      if (!regions?.length) {
        return null
      }

      // Find region that contains the country
      const region = regions.find(region =>
        region.countries?.some((country: any) => country.iso_2 === countryCode)
      )

      return region || null
    } catch (error) {
      console.error('Error getting region by country code:', error)
      return null
    }
  }

  // List products
  static async listProducts(params: {
    pageParam?: number
    queryParams?: any
    countryCode?: string
    regionId?: string
  }): Promise<{
    response: { products: any[]; count: number }
    nextPage: number | null
    queryParams?: any
  }> {
    const { pageParam = 1, queryParams = {}, countryCode, regionId } = params

    if (!countryCode && !regionId) {
      throw new Error("Country code or region ID is required")
    }

    const limit = queryParams.limit || 12
    const _pageParam = Math.max(pageParam, 1)
    const offset = (_pageParam === 1) ? 0 : (_pageParam - 1) * limit

    let region: any | null = null

    if (countryCode) {
      region = await this.getRegion(countryCode)
    } else if (regionId) {
      region = await this.retrieveRegion(regionId)
    }

    if (!region) {
      return {
        response: { products: [], count: 0 },
        nextPage: null,
      }
    }

    try {
      const response = await this.fetch<{ products: any[]; count: number }>(
        '/store/products',
        {
          method: 'GET',
          query: {
            limit,
            offset,
            region_id: region.id,
            fields: "*variants.calculated_price,+variants.inventory_quantity,+metadata,+tags",
            ...queryParams,
          },
          cache: 'force-cache',
        }
      )

      const nextPage = response.count > offset + limit ? pageParam + 1 : null

      return {
        response: {
          products: response.products,
          count: response.count,
        },
        nextPage,
        queryParams,
      }
    } catch (error) {
      console.error('Error fetching products:', error)
      return {
        response: { products: [], count: 0 },
        nextPage: null,
      }
    }
  }

  // Get single product
  static async retrieveProduct(
    id: string,
    queryParams?: any
  ): Promise<any | null> {
    try {
      const response = await this.fetch<{ product: any }>(`/store/products/${id}`, {
        method: 'GET',
        query: {
          fields: "*variants.calculated_price,+variants.inventory_quantity,+metadata,+tags",
          ...queryParams,
        },
        cache: 'force-cache',
      })

      return response.product
    } catch (error) {
      console.error('Error fetching product:', error)
      return null
    }
  }

  // Search products
  static async searchProducts(params: {
    query: string
    regionId?: string
    countryCode?: string
    limit?: number
    offset?: number
  }): Promise<{
    response: { products: any[]; count: number }
    nextPage: number | null
  }> {
    const { query, regionId, countryCode, limit = 12, offset = 0 } = params

    return this.listProducts({
      pageParam: Math.floor(offset / limit) + 1,
      queryParams: {
        q: query,
        limit,
        offset: offset % limit
      },
      regionId,
      countryCode
    })
  }
}
