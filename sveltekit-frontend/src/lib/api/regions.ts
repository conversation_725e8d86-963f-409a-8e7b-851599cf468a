import { MedusaAPI } from './medusa'

// Region cache to avoid repeated API calls
const regionMap = new Map<string, any>()
let regionsCache: any[] | null = null
let regionsCacheTime = 0
const CACHE_DURATION = 3600 * 1000 // 1 hour

export class RegionsAPI {
  // List all regions
  static async listRegions(): Promise<any[]> {
    // Check cache first
    if (regionsCache && Date.now() - regionsCacheTime < CACHE_DURATION) {
      return regionsCache
    }

    try {
      const regions = await MedusaAPI.listRegions()

      // Update cache
      regionsCache = regions
      regionsCacheTime = Date.now()

      // Update region map for country lookups
      regionMap.clear()
      regions.forEach((region) => {
        region.countries?.forEach((country: any) => {
          if (country.iso_2) {
            regionMap.set(country.iso_2, region)
          }
        })
      })

      return regions
    } catch (error) {
      console.error('Error listing regions:', error)
      return []
    }
  }

  // Get region by ID
  static async retrieveRegion(id: string): Promise<any | null> {
    try {
      return await MedusaAPI.retrieveRegion(id)
    } catch (error) {
      console.error('Error retrieving region:', error)
      return null
    }
  }

  // Get region by country code
  static async getRegion(countryCode: string): Promise<any | null> {
    try {
      // Check cache first
      if (regionMap.has(countryCode)) {
        return regionMap.get(countryCode) || null
      }

      // If not in cache, refresh regions
      const regions = await this.listRegions()

      if (!regions?.length) {
        return null
      }

      // Return the region for this country code
      return regionMap.get(countryCode) || null
    } catch (error) {
      console.error('Error getting region by country code:', error)
      return null
    }
  }

  // Get default region (fallback to US or first available)
  static async getDefaultRegion(): Promise<any | null> {
    try {
      // Try to get US region first
      let region = await this.getRegion('us')

      if (!region) {
        // Fallback to first available region
        const regions = await this.listRegions()
        region = regions?.[0] || null
      }

      return region
    } catch (error) {
      console.error('Error getting default region:', error)
      return null
    }
  }

  // Legacy method for backward compatibility
  static async getRegionByCountry(countryCode: string): Promise<any | null> {
    return this.getRegion(countryCode)
  }

  // Get all country codes from all regions
  static async getCountryCodes(): Promise<string[]> {
    try {
      const regions = await this.listRegions()
      const countryCodes: string[] = []

      regions.forEach((region) => {
        region.countries?.forEach((country: any) => {
          if (country.iso_2) {
            countryCodes.push(country.iso_2)
          }
        })
      })

      return [...new Set(countryCodes)] // Remove duplicates
    } catch (error) {
      console.error('Error getting country codes:', error)
      return []
    }
  }

  // Check if a country code is supported
  static async isCountrySupported(countryCode: string): Promise<boolean> {
    try {
      const region = await this.getRegion(countryCode)
      return !!region
    } catch (error) {
      console.error('Error checking country support:', error)
      return false
    }
  }

  // Clear cache (useful for testing or manual refresh)
  static clearCache(): void {
    regionMap.clear()
    regionsCache = null
    regionsCacheTime = 0
  }
}
