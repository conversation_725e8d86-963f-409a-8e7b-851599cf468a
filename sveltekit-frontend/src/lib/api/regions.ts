import { browser } from '$app/environment'
import type { Region } from '$lib/types'

export class RegionsAPI {
  // Get all regions
  static async listRegions(): Promise<Region[]> {
    if (!browser) {
      // Return mock region for SSR
      return [{
        id: 'region_1',
        name: 'United States',
        currency_code: 'USD',
        countries: [{
          id: 'country_1',
          iso_2: 'US',
          name: 'United States'
        }]
      }]
    }

    try {
      // Mock regions for demonstration
      const mockRegions: Region[] = [
        {
          id: 'region_1',
          name: 'United States',
          currency_code: 'USD',
          countries: [{
            id: 'country_1',
            iso_2: 'US',
            name: 'United States'
          }]
        }
      ]

      return mockRegions
    } catch (error) {
      console.error('Error fetching regions:', error)
      throw error
    }
  }

  // Get region by ID
  static async getRegion(id: string): Promise<Region> {
    try {
      const regions = await this.listRegions()
      const region = regions.find(r => r.id === id)
      if (!region) throw new Error('Region not found')
      return region
    } catch (error) {
      console.error('Error fetching region:', error)
      throw error
    }
  }

  // Get region by country code
  static async getRegionByCountry(countryCode: string): Promise<Region | null> {
    try {
      const regions = await this.listRegions()
      return regions.find(region =>
        region.countries?.some(country =>
          country.iso_2?.toLowerCase() === countryCode.toLowerCase()
        )
      ) || null
    } catch (error) {
      console.error('Error fetching region by country:', error)
      return null
    }
  }
}
