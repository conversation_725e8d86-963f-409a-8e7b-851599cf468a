import { MedusaAPI } from './medusa'
import type { Product, ProductFilters, PaginationParams, ProductListResponse } from '$lib/types'

// Transform Medusa product to our Product type
function transformMedusaProduct(medusaProduct: any): Product {
  return {
    id: medusaProduct.id,
    handle: medusaProduct.handle || '',
    title: medusaProduct.title,
    description: medusaProduct.description || '',
    thumbnail: medusaProduct.thumbnail || '',
    variants: medusaProduct.variants?.map((variant: any) => ({
      id: variant.id,
      title: variant.title,
      inventory_quantity: variant.inventory_quantity || 0,
      calculated_price: {
        calculated_amount: variant.calculated_price?.calculated_amount || 0,
        currency_code: variant.calculated_price?.currency_code || 'USD'
      },
      options: variant.options || []
    })) || [],
    images: medusaProduct.images?.map((image: any) => ({
      id: image.id,
      url: image.url
    })) || [],
    metadata: medusaProduct.metadata || {},
    tags: medusaProduct.tags?.map((tag: any) => tag.value || '') || [],
    material: medusaProduct.material || '',
    origin_country: medusaProduct.origin_country || '',
    weight: medusaProduct.weight || 0,
    length: medusaProduct.length || 0,
    width: medusaProduct.width || 0,
    height: medusaProduct.height || 0
  }
}

export class ProductsAPI {
  // List products with filters and pagination
  static async listProducts(
    regionId: string,
    filters: ProductFilters = {},
    pagination: PaginationParams = {}
  ): Promise<ProductListResponse> {
    try {
      const { limit = 12, offset = 0 } = pagination
      const pageParam = Math.floor(offset / limit) + 1

      // Build query parameters for Medusa API
      const queryParams: any = {
        limit,
        offset: offset % limit
      }

      // Add search query
      if (filters.q) {
        queryParams.q = filters.q
      }

      // Add category filter
      if (filters.category_id?.length) {
        queryParams.category_id = filters.category_id
      }

      // Add collection filter
      if (filters.collection_id?.length) {
        queryParams.collection_id = filters.collection_id
      }

      // Call Medusa API
      const response = await MedusaAPI.listProducts({
        pageParam,
        queryParams,
        regionId
      })

      // Transform products
      const transformedProducts = response.response.products.map(transformMedusaProduct)

      return {
        products: transformedProducts,
        count: response.response.count,
        nextPage: response.nextPage,
      }
    } catch (error) {
      console.error('Error fetching products:', error)

      // Fallback to empty response on error
      return {
        products: [],
        count: 0,
        nextPage: null,
      }
    }
  }

  // Get single product by ID
  static async getProduct(id: string, regionId: string): Promise<Product> {
    try {
      const medusaProduct = await MedusaAPI.retrieveProduct(id, { region_id: regionId })

      if (!medusaProduct) {
        throw new Error('Product not found')
      }

      return transformMedusaProduct(medusaProduct)
    } catch (error) {
      console.error('Error fetching product:', error)
      throw error
    }
  }

  // Get single product by handle
  static async getProductByHandle(handle: string, regionId: string): Promise<Product> {
    try {
      // Use list products with handle filter to find the product
      const response = await MedusaAPI.listProducts({
        pageParam: 1,
        queryParams: {
          handle,
          limit: 1
        },
        regionId
      })

      if (!response.response.products.length) {
        throw new Error('Product not found')
      }

      return transformMedusaProduct(response.response.products[0])
    } catch (error) {
      console.error('Error fetching product by handle:', error)
      throw error
    }
  }

  // Search products
  static async searchProducts(
    query: string,
    regionId: string,
    pagination: PaginationParams = {}
  ): Promise<ProductListResponse> {
    return this.listProducts(regionId, { q: query }, pagination)
  }

  // Get products by category
  static async getProductsByCategory(
    categoryId: string,
    regionId: string,
    pagination: PaginationParams = {}
  ): Promise<ProductListResponse> {
    return this.listProducts(regionId, { category_id: [categoryId] }, pagination)
  }

  // Get products by collection
  static async getProductsByCollection(
    collectionId: string,
    regionId: string,
    pagination: PaginationParams = {}
  ): Promise<ProductListResponse> {
    return this.listProducts(regionId, { collection_id: [collectionId] }, pagination)
  }

  // Get featured products (you can customize this logic)
  static async getFeaturedProducts(
    regionId: string,
    limit: number = 8
  ): Promise<Product[]> {
    try {
      const response = await this.listProducts(regionId, {}, { limit, offset: 0 })
      return response.products
    } catch (error) {
      console.error('Error fetching featured products:', error)
      throw error
    }
  }
}
