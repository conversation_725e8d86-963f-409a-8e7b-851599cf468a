import { browser } from '$app/environment'
import { BACKEND_URL } from '$lib/config'
import type { Product, ProductFilters, PaginationParams, ProductListResponse } from '$lib/types'

export class ProductsAPI {
  // List products with filters and pagination
  static async listProducts(
    regionId: string,
    filters: ProductFilters = {},
    pagination: PaginationParams = {}
  ): Promise<ProductListResponse> {
    // For now, return mock data until we can properly integrate with Medusa
    if (!browser) {
      return {
        products: [],
        count: 0,
        nextPage: null,
      }
    }

    try {
      const { limit = 12, offset = 0 } = pagination

      // Get mock products
      const allProducts = await this.getMockProducts()

      // Apply filters
      let filteredProducts = allProducts

      if (filters.q) {
        const query = filters.q.toLowerCase()
        filteredProducts = filteredProducts.filter(product =>
          product.title.toLowerCase().includes(query) ||
          product.description?.toLowerCase().includes(query)
        )
      }

      if (filters.category_id?.length) {
        // Mock category filtering - in real implementation this would filter by category
        filteredProducts = filteredProducts.filter(product =>
          filters.category_id?.includes('category_1') // Mock category
        )
      }

      if (filters.collection_id?.length) {
        // Mock collection filtering
        filteredProducts = filteredProducts.filter(product =>
          filters.collection_id?.includes('collection_1') // Mock collection
        )
      }

      // Apply pagination
      const paginatedProducts = filteredProducts.slice(offset, offset + limit)
      const hasNextPage = offset + limit < filteredProducts.length

      return {
        products: paginatedProducts,
        count: filteredProducts.length,
        nextPage: hasNextPage ? Math.floor((offset + limit) / limit) + 1 : null,
      }
    } catch (error) {
      console.error('Error fetching products:', error)
      throw error
    }
  }

  // Get single product by ID
  static async getProduct(id: string, regionId: string): Promise<Product> {
    if (!browser) {
      throw new Error('Product API only available on client side')
    }

    try {
      // Get product from mock data
      const mockProducts = await this.getMockProducts()
      const product = mockProducts.find(p => p.id === id)

      if (!product) {
        throw new Error('Product not found')
      }

      return product
    } catch (error) {
      console.error('Error fetching product:', error)
      throw error
    }
  }

  // Get single product by handle
  static async getProductByHandle(handle: string, regionId: string): Promise<Product> {
    if (!browser) {
      throw new Error('Product API only available on client side')
    }

    try {
      // Get product from mock data
      const mockProducts = await this.getMockProducts()
      const product = mockProducts.find(p => p.handle === handle)

      if (!product) {
        throw new Error('Product not found')
      }

      return product
    } catch (error) {
      console.error('Error fetching product:', error)
      throw error
    }
  }

  // Get mock products data
  private static async getMockProducts(): Promise<Product[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300))

    return [
      {
        id: '1',
        handle: 'premium-vape-kit',
        title: 'Premium Vape Kit',
        description: 'High-quality vaping device with advanced features. This premium kit includes everything you need to get started with vaping. Features include adjustable airflow, long-lasting battery, and premium build quality.',
        thumbnail: 'https://via.placeholder.com/600x600?text=Premium+Vape+Kit',
        variants: [{
          id: 'var1',
          title: 'Standard',
          inventory_quantity: 10,
          calculated_price: {
            calculated_amount: 4999,
            currency_code: 'USD'
          },
          options: []
        }],
        images: [
          {
            id: 'img1',
            url: 'https://via.placeholder.com/600x600?text=Premium+Vape+Kit+1'
          },
          {
            id: 'img2',
            url: 'https://via.placeholder.com/600x600?text=Premium+Vape+Kit+2'
          },
          {
            id: 'img3',
            url: 'https://via.placeholder.com/600x600?text=Premium+Vape+Kit+3'
          }
        ],
        metadata: {},
        tags: [],
        material: 'Stainless Steel',
        origin_country: 'China',
        weight: 250,
        length: 120,
        width: 25,
        height: 25
      },
      {
        id: '2',
        handle: 'e-liquid-collection',
        title: 'E-Liquid Collection',
        description: 'Premium e-liquids in various flavors. Our collection features the finest ingredients and carefully crafted flavor profiles. Available in multiple nicotine strengths.',
        thumbnail: 'https://via.placeholder.com/600x600?text=E-Liquid+Collection',
        variants: [{
          id: 'var2',
          title: 'Standard',
          inventory_quantity: 25,
          calculated_price: {
            calculated_amount: 1999,
            currency_code: 'USD'
          },
          options: []
        }],
        images: [
          {
            id: 'img4',
            url: 'https://via.placeholder.com/600x600?text=E-Liquid+Collection+1'
          },
          {
            id: 'img5',
            url: 'https://via.placeholder.com/600x600?text=E-Liquid+Collection+2'
          }
        ],
        metadata: {},
        tags: [],
        material: 'Glass',
        origin_country: 'USA',
        weight: 50,
        length: 80,
        width: 20,
        height: 20
      },
      {
        id: '3',
        handle: 'starter-kit',
        title: 'Beginner Starter Kit',
        description: 'Perfect for those new to vaping. This comprehensive starter kit includes everything needed for beginners.',
        thumbnail: 'https://via.placeholder.com/600x600?text=Starter+Kit',
        variants: [{
          id: 'var3',
          title: 'Standard',
          inventory_quantity: 15,
          calculated_price: {
            calculated_amount: 2999,
            currency_code: 'USD'
          },
          options: []
        }],
        images: [
          {
            id: 'img6',
            url: 'https://via.placeholder.com/600x600?text=Starter+Kit+1'
          }
        ],
        metadata: {},
        tags: [],
        material: 'Aluminum',
        origin_country: 'China',
        weight: 180,
        length: 100,
        width: 22,
        height: 22
      }
    ]
  }

  // Search products
  static async searchProducts(
    query: string,
    regionId: string,
    pagination: PaginationParams = {}
  ): Promise<ProductListResponse> {
    return this.listProducts(regionId, { q: query }, pagination)
  }

  // Get products by category
  static async getProductsByCategory(
    categoryId: string,
    regionId: string,
    pagination: PaginationParams = {}
  ): Promise<ProductListResponse> {
    return this.listProducts(regionId, { category_id: [categoryId] }, pagination)
  }

  // Get products by collection
  static async getProductsByCollection(
    collectionId: string,
    regionId: string,
    pagination: PaginationParams = {}
  ): Promise<ProductListResponse> {
    return this.listProducts(regionId, { collection_id: [collectionId] }, pagination)
  }

  // Get featured products (you can customize this logic)
  static async getFeaturedProducts(
    regionId: string,
    limit: number = 8
  ): Promise<Product[]> {
    try {
      const response = await this.listProducts(regionId, {}, { limit, offset: 0 })
      return response.products
    } catch (error) {
      console.error('Error fetching featured products:', error)
      throw error
    }
  }
}
