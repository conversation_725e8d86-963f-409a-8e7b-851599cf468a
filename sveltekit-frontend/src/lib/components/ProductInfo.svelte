<script lang="ts">
  import type { Product } from '$lib/types'

  export let product: Product
</script>

<div id="product-info">
  <div class="flex flex-col gap-y-4 lg:max-w-[500px] mx-auto">
    {#if product.collection}
      <a
        href="/collections/{product.collection.handle}"
        class="text-medium text-gray-600 hover:text-gray-800 transition-colors"
      >
        {product.collection.title}
      </a>
    {/if}
    
    <h2 
      class="text-3xl leading-10 text-gray-900 font-bold"
      data-testid="product-title"
    >
      {product.title}
    </h2>

    {#if product.description}
      <p 
        class="text-medium text-gray-600 whitespace-pre-line"
        data-testid="product-description"
      >
        {product.description}
      </p>
    {/if}
  </div>
</div>
