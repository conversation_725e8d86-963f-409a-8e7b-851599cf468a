<script lang="ts">
  import type { InputProps } from '$lib/types'
  
  interface $$Props extends InputProps {
    id?: string
    name?: string
    value?: string
  }
  
  export let type: InputProps['type'] = 'text'
  export let placeholder: InputProps['placeholder'] = ''
  export let value: InputProps['value'] = ''
  export let disabled: InputProps['disabled'] = false
  export let required: InputProps['required'] = false
  export let error: InputProps['error'] = ''
  export let id: string = ''
  export let name: string = ''
  
  let className = ''
  export { className as class }
  
  $: baseClasses = 'input block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 sm:text-sm'
  
  $: stateClasses = error 
    ? 'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500'
    : 'border-gray-300 focus:ring-primary-500 focus:border-primary-500'
    
  $: classes = `${baseClasses} ${stateClasses} ${className}`
</script>

<div class="space-y-1">
  <input
    {type}
    {placeholder}
    {value}
    {disabled}
    {required}
    {id}
    {name}
    class={classes}
    on:input
    on:change
    on:focus
    on:blur
    {...$$restProps}
  />
  {#if error}
    <p class="text-sm text-red-600">{error}</p>
  {/if}
</div>
