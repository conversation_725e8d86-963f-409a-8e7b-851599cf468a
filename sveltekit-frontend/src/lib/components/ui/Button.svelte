<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import type { ButtonProps } from '$lib/types'
  
  interface $$Props extends ButtonProps {
    onclick?: () => void
  }
  
  export let variant: ButtonProps['variant'] = 'primary'
  export let size: ButtonProps['size'] = 'md'
  export let disabled: ButtonProps['disabled'] = false
  export let loading: ButtonProps['loading'] = false
  export let type: ButtonProps['type'] = 'button'
  
  let className = ''
  export { className as class }
  
  const dispatch = createEventDispatcher()
  
  $: baseClasses = 'btn inline-flex items-center justify-center font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'
  
  $: variantClasses = {
    primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-primary-500',
    ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500'
  }
  
  $: sizeClasses = {
    sm: 'px-3 py-1.5 text-sm rounded',
    md: 'px-4 py-2 text-sm rounded-md',
    lg: 'px-6 py-3 text-base rounded-md'
  }
  
  $: classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`
  
  function handleClick() {
    if (!disabled && !loading) {
      dispatch('click')
    }
  }
</script>

<button
  {type}
  class={classes}
  {disabled}
  on:click={handleClick}
  {...$$restProps}
>
  {#if loading}
    <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  {/if}
  <slot />
</button>
