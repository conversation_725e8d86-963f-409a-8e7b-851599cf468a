<script lang="ts">
  import { cartStore } from '$lib/stores/cart'
  import Button from '$lib/components/ui/Button.svelte'
  import type { Product, Region } from '$lib/types'

  export let product: Product
  export let region: Region
  export let disabled = false

  let isAdding = false
  let selectedVariant = product.variants?.[0] // Default to first variant
  
  $: inStock = selectedVariant && (selectedVariant.inventory_quantity || 0) > 0
  $: price = selectedVariant?.calculated_price?.calculated_amount
  $: formattedPrice = price ? new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: selectedVariant?.calculated_price?.currency_code || 'USD'
  }).format(price / 100) : 'N/A'

  async function handleAddToCart() {
    if (!selectedVariant?.id || !inStock || disabled || isAdding) return

    isAdding = true
    
    try {
      await cartStore.addItem({
        variantId: selectedVariant.id,
        quantity: 1
      })
      
      // Show success feedback (you could add a toast notification here)
      console.log('Added to cart:', product.title)
    } catch (error) {
      console.error('Failed to add to cart:', error)
      // Show error feedback
    } finally {
      isAdding = false
    }
  }
</script>

<div class="flex flex-col gap-y-6">
  <!-- Product Price -->
  <div class="flex flex-col gap-y-2">
    <span class="text-2xl font-bold text-gray-900">
      {formattedPrice}
    </span>
    
    {#if selectedVariant && !inStock}
      <span class="text-sm text-red-600 font-medium">Out of stock</span>
    {/if}
  </div>

  <!-- Variant Selection (if multiple variants exist) -->
  {#if product.variants && product.variants.length > 1}
    <div class="flex flex-col gap-y-2">
      <label for="variant-select" class="text-sm font-medium text-gray-700">
        Variant
      </label>
      <select
        id="variant-select"
        bind:value={selectedVariant}
        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
        disabled={disabled || isAdding}
      >
        {#each product.variants as variant}
          <option value={variant}>
            {variant.title} - {new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: variant.calculated_price?.currency_code || 'USD'
            }).format((variant.calculated_price?.calculated_amount || 0) / 100)}
          </option>
        {/each}
      </select>
    </div>
  {/if}

  <!-- Add to Cart Button -->
  <Button
    variant="primary"
    size="lg"
    disabled={!inStock || !selectedVariant || disabled || isAdding}
    loading={isAdding}
    class="w-full"
    on:click={handleAddToCart}
    data-testid="add-product-button"
  >
    {#if !selectedVariant}
      Select variant
    {:else if !inStock}
      Out of stock
    {:else}
      Add to cart
    {/if}
  </Button>
</div>
