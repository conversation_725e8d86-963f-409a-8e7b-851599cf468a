<script lang="ts">
  import { page } from '$app/stores'
  import { goto } from '$app/navigation'
  import { cartItemsCount } from '$lib/stores/cart'
  import { customer, isAuthenticated } from '$lib/stores/customer'
  import Button from '$lib/components/ui/Button.svelte'
  
  let mobileMenuOpen = false
  
  function toggleMobileMenu() {
    mobileMenuOpen = !mobileMenuOpen
  }
  
  function handleCartClick() {
    goto('/cart')
  }
  
  function handleAccountClick() {
    if ($isAuthenticated) {
      goto('/account')
    } else {
      goto('/login')
    }
  }
</script>

<header class="bg-white shadow-sm border-b border-gray-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo -->
      <div class="flex items-center">
        <a href="/" class="flex items-center space-x-2">
          <div class="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-lg">J</span>
          </div>
          <span class="text-xl font-bold text-gray-900">Jani Vapes</span>
        </a>
      </div>
      
      <!-- Desktop Navigation -->
      <nav class="hidden md:flex items-center space-x-8">
        <a 
          href="/products" 
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200"
          class:text-primary-600={$page.url.pathname.startsWith('/products')}
        >
          Products
        </a>
        <a 
          href="/collections" 
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200"
          class:text-primary-600={$page.url.pathname.startsWith('/collections')}
        >
          Collections
        </a>
        <a 
          href="/about" 
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200"
          class:text-primary-600={$page.url.pathname === '/about'}
        >
          About
        </a>
      </nav>
      
      <!-- Right side actions -->
      <div class="flex items-center space-x-4">
        <!-- Search (placeholder for now) -->
        <button class="text-gray-700 hover:text-primary-600 transition-colors duration-200">
          <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </button>
        
        <!-- Account -->
        <button 
          on:click={handleAccountClick}
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200"
        >
          <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        </button>
        
        <!-- Cart -->
        <button 
          on:click={handleCartClick}
          class="relative text-gray-700 hover:text-primary-600 transition-colors duration-200"
        >
          <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
          </svg>
          {#if $cartItemsCount > 0}
            <span class="absolute -top-2 -right-2 bg-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {$cartItemsCount}
            </span>
          {/if}
        </button>
        
        <!-- Mobile menu button -->
        <button 
          on:click={toggleMobileMenu}
          class="md:hidden text-gray-700 hover:text-primary-600 transition-colors duration-200"
        >
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            {#if mobileMenuOpen}
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            {:else}
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            {/if}
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Mobile Navigation -->
    {#if mobileMenuOpen}
      <div class="md:hidden border-t border-gray-200 py-4">
        <nav class="flex flex-col space-y-4">
          <a 
            href="/products" 
            class="text-gray-700 hover:text-primary-600 transition-colors duration-200 px-2 py-1"
            on:click={() => mobileMenuOpen = false}
          >
            Products
          </a>
          <a 
            href="/collections" 
            class="text-gray-700 hover:text-primary-600 transition-colors duration-200 px-2 py-1"
            on:click={() => mobileMenuOpen = false}
          >
            Collections
          </a>
          <a 
            href="/about" 
            class="text-gray-700 hover:text-primary-600 transition-colors duration-200 px-2 py-1"
            on:click={() => mobileMenuOpen = false}
          >
            About
          </a>
          
          {#if $isAuthenticated}
            <a 
              href="/account" 
              class="text-gray-700 hover:text-primary-600 transition-colors duration-200 px-2 py-1"
              on:click={() => mobileMenuOpen = false}
            >
              My Account
            </a>
          {:else}
            <a 
              href="/login" 
              class="text-gray-700 hover:text-primary-600 transition-colors duration-200 px-2 py-1"
              on:click={() => mobileMenuOpen = false}
            >
              Login
            </a>
          {/if}
        </nav>
      </div>
    {/if}
  </div>
</header>
