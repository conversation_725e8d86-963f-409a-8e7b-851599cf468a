<script lang="ts">
  import type { Product } from '$lib/types'

  export let product: Product
  
  $: images = product.images || []
  $: hasImages = images.length > 0
  $: fallbackImage = product.thumbnail || 'https://via.placeholder.com/600x600?text=No+Image'
</script>

<div class="flex items-start relative">
  <div class="flex flex-col flex-1 small:mx-16 gap-y-4">
    {#if hasImages}
      {#each images as image, index}
        <div
          class="relative aspect-[29/34] w-full overflow-hidden bg-gray-100 rounded-lg"
          id={image.id}
        >
          <img
            src={image.url}
            alt="Product image {index + 1}"
            class="absolute inset-0 w-full h-full object-cover rounded-lg"
            loading={index <= 2 ? 'eager' : 'lazy'}
          />
        </div>
      {/each}
    {:else}
      <!-- Fallback to thumbnail if no images -->
      <div class="relative aspect-[29/34] w-full overflow-hidden bg-gray-100 rounded-lg">
        <img
          src={fallbackImage}
          alt={product.title}
          class="absolute inset-0 w-full h-full object-cover rounded-lg"
          loading="eager"
        />
      </div>
    {/if}
  </div>
</div>
