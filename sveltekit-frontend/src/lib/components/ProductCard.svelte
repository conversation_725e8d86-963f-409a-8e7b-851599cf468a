<script lang="ts">
  import { cartStore } from '$lib/stores/cart'
  import Button from '$lib/components/ui/Button.svelte'
  import type { Product } from '$lib/types'

  export let product: Product

  let isAddingToCart = false

  // Get the first variant for the add to cart functionality
  $: firstVariant = product.variants?.[0]
  $: hasStock = firstVariant && (firstVariant.inventory_quantity || 0) > 0
  $: price = firstVariant?.calculated_price?.calculated_amount
  $: formattedPrice = price ? new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: firstVariant?.calculated_price?.currency_code || 'USD'
  }).format(price / 100) : 'N/A'

  // Get the first image
  $: thumbnail = product.thumbnail || product.images?.[0]?.url
  $: productUrl = product.handle ? `/us/products/${product.handle}` : '#'

  async function handleAddToCart() {
    if (!firstVariant || !hasStock || isAddingToCart) return

    isAddingToCart = true

    try {
      await cartStore.addItem({
        variantId: firstVariant.id,
        quantity: 1
      })
    } catch (error) {
      console.error('Failed to add to cart:', error)
      // You could show a toast notification here
    } finally {
      isAddingToCart = false
    }
  }
</script>

<div class="card group hover:shadow-md transition-shadow duration-200">
  <!-- Product Image -->
  <a href={productUrl} class="block aspect-square overflow-hidden rounded-lg bg-gray-100 mb-4">
    {#if thumbnail}
      <img
        src={thumbnail}
        alt={product.title}
        class="h-full w-full object-cover object-center group-hover:scale-105 transition-transform duration-200"
        loading="lazy"
      />
    {:else}
      <div class="h-full w-full flex items-center justify-center text-gray-400">
        <svg class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>
    {/if}
  </a>

  <!-- Product Info -->
  <div class="space-y-2">
    <h3 class="text-lg font-medium text-gray-900 line-clamp-2">
      <a href={productUrl} class="hover:text-primary-600 transition-colors">
        {product.title}
      </a>
    </h3>

    {#if product.description}
      <p class="text-sm text-gray-600 line-clamp-2">
        {product.description}
      </p>
    {/if}

    <!-- Price -->
    <div class="flex items-center justify-between">
      <span class="text-lg font-semibold text-gray-900">
        {formattedPrice}
      </span>

      {#if !hasStock}
        <span class="text-sm text-red-600 font-medium">Out of Stock</span>
      {/if}
    </div>

    <!-- Add to Cart Button -->
    <div class="pt-2">
      <Button
        variant="primary"
        size="md"
        disabled={!hasStock || isAddingToCart}
        loading={isAddingToCart}
        class="w-full"
        on:click={handleAddToCart}
      >
        {#if !hasStock}
          Out of Stock
        {:else if isAddingToCart}
          Adding...
        {:else}
          Add to Cart
        {/if}
      </Button>
    </div>
  </div>
</div>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
