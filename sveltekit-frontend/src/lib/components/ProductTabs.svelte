<script lang="ts">
  import type { Product } from '$lib/types'

  export let product: Product

  let activeTab = 'info'

  const tabs = [
    { id: 'info', label: 'Product Information' },
    { id: 'shipping', label: 'Shipping & Returns' }
  ]

  function setActiveTab(tabId: string) {
    activeTab = tabId
  }
</script>

<div class="w-full">
  <!-- Tab Navigation -->
  <div class="border-b border-gray-200">
    <nav class="-mb-px flex space-x-8">
      {#each tabs as tab}
        <button
          class="py-2 px-1 border-b-2 font-medium text-sm transition-colors {activeTab === tab.id
            ? 'border-primary-500 text-primary-600'
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}"
          on:click={() => setActiveTab(tab.id)}
        >
          {tab.label}
        </button>
      {/each}
    </nav>
  </div>

  <!-- Tab Content -->
  <div class="mt-6">
    {#if activeTab === 'info'}
      <div class="text-sm text-gray-600 space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
          <div class="space-y-4">
            <div>
              <span class="font-semibold text-gray-900">Material</span>
              <p>{product.material || '-'}</p>
            </div>
            <div>
              <span class="font-semibold text-gray-900">Country of origin</span>
              <p>{product.origin_country || '-'}</p>
            </div>
            <div>
              <span class="font-semibold text-gray-900">Type</span>
              <p>{product.type?.value || '-'}</p>
            </div>
          </div>
          <div class="space-y-4">
            <div>
              <span class="font-semibold text-gray-900">Weight</span>
              <p>{product.weight ? `${product.weight} g` : '-'}</p>
            </div>
            <div>
              <span class="font-semibold text-gray-900">Dimensions</span>
              <p>
                {#if product.length && product.width && product.height}
                  {product.length} x {product.width} x {product.height} mm
                {:else}
                  -
                {/if}
              </p>
            </div>
          </div>
        </div>
      </div>
    {:else if activeTab === 'shipping'}
      <div class="text-sm text-gray-600 space-y-6">
        <div class="space-y-6">
          <div class="flex items-start gap-x-3">
            <div class="flex-shrink-0 w-6 h-6 text-primary-600">
              <!-- Fast Delivery Icon -->
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div>
              <span class="font-semibold text-gray-900">Fast delivery</span>
              <p class="max-w-sm">
                Your package will arrive in 3-5 business days at your pick up
                location or in the comfort of your home.
              </p>
            </div>
          </div>
          <div class="flex items-start gap-x-3">
            <div class="flex-shrink-0 w-6 h-6 text-primary-600">
              <!-- Refresh Icon -->
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </div>
            <div>
              <span class="font-semibold text-gray-900">Simple exchanges</span>
              <p class="max-w-sm">
                Is the fit not quite right? No worries - we'll exchange your
                product for a new one.
              </p>
            </div>
          </div>
          <div class="flex items-start gap-x-3">
            <div class="flex-shrink-0 w-6 h-6 text-primary-600">
              <!-- Back Arrow Icon -->
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
            </div>
            <div>
              <span class="font-semibold text-gray-900">Easy returns</span>
              <p class="max-w-sm">
                Just return your product and we'll refund your money. No
                questions asked – we'll do our best to make sure your return is
                hassle-free.
              </p>
            </div>
          </div>
        </div>
      </div>
    {/if}
  </div>
</div>
