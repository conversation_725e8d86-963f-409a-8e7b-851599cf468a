import { writable, derived } from 'svelte/store'
import { browser } from '$app/environment'
import type { Cart, CartState, AddToCartParams, UpdateCartItemParams } from '$lib/types'

// Cart state store
const createCartStore = () => {
  const { subscribe, set, update } = writable<CartState>({
    cart: null,
    isLoading: false,
    error: null
  })

  return {
    subscribe,

    // Initialize cart (get existing or create new)
    async initialize(regionId?: string) {
      update(state => ({ ...state, isLoading: true, error: null }))

      try {
        let cartId = browser ? localStorage.getItem('cart_id') : null
        let cart: Cart | null = null

        // Try to retrieve existing cart from localStorage
        if (cartId && browser) {
          const storedCart = localStorage.getItem(`cart_${cartId}`)
          if (storedCart) {
            cart = JSON.parse(storedCart)
          }
        }

        // Create new cart if none exists
        if (!cart && regionId) {
          cart = {
            id: `cart_${Date.now()}`,
            region_id: regionId,
            currency_code: 'USD',
            items: [],
            subtotal: 0,
            total: 0,
            tax_total: 0,
            shipping_total: 0
          }

          if (browser) {
            localStorage.setItem('cart_id', cart.id)
            localStorage.setItem(`cart_${cart.id}`, JSON.stringify(cart))
          }
        }

        set({ cart, isLoading: false, error: null })
        return cart
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to initialize cart'
        set({ cart: null, isLoading: false, error: errorMessage })
        throw error
      }
    },

    // Add item to cart
    async addItem({ variantId, quantity }: AddToCartParams) {
      update(state => ({ ...state, isLoading: true, error: null }))

      try {
        // Get current state synchronously
        let currentState: CartState
        const unsubscribe = subscribe(state => {
          currentState = state
        })
        unsubscribe()

        if (!currentState!.cart) {
          throw new Error('No cart available')
        }

        // Check if item already exists (prevent duplicates as per user preference)
        const existingItem = currentState!.cart.items?.find(item => item.variant_id === variantId)

        let updatedCart
        if (existingItem) {
          // Update existing item quantity instead of adding duplicate
          const updatedItems = currentState!.cart.items?.map(item =>
            item.variant_id === variantId
              ? { ...item, quantity: (item.quantity || 0) + quantity }
              : item
          ) || []

          updatedCart = {
            ...currentState!.cart,
            items: updatedItems,
            subtotal: updatedItems.reduce((sum, item) => sum + ((item.unit_price || 0) * (item.quantity || 0)), 0),
            total: updatedItems.reduce((sum, item) => sum + ((item.unit_price || 0) * (item.quantity || 0)), 0)
          }
        } else {
          // Add new item - in a real implementation, you'd fetch product details by variant ID
          // For now, we'll use mock data based on the variant ID
          let productData = {
            product_title: 'Premium Vape Kit',
            variant_title: 'Standard',
            unit_price: 4999,
            thumbnail: 'https://via.placeholder.com/300x300?text=Vape+Kit'
          }

          // Mock different products based on variant ID
          if (variantId === 'var2') {
            productData = {
              product_title: 'E-Liquid Collection',
              variant_title: 'Standard',
              unit_price: 1999,
              thumbnail: 'https://via.placeholder.com/300x300?text=E-Liquid'
            }
          } else if (variantId === 'var3') {
            productData = {
              product_title: 'Beginner Starter Kit',
              variant_title: 'Standard',
              unit_price: 2999,
              thumbnail: 'https://via.placeholder.com/300x300?text=Starter+Kit'
            }
          }

          const newItem = {
            id: `item_${Date.now()}`,
            variant_id: variantId,
            ...productData,
            quantity
          }

          updatedCart = {
            ...currentState!.cart,
            items: [...(currentState!.cart.items || []), newItem],
            subtotal: (currentState!.cart.subtotal || 0) + (newItem.unit_price * quantity),
            total: (currentState!.cart.total || 0) + (newItem.unit_price * quantity)
          }
        }

        // Save to localStorage
        if (browser) {
          localStorage.setItem(`cart_${updatedCart.id}`, JSON.stringify(updatedCart))
        }

        update(state => ({ ...state, cart: updatedCart, isLoading: false }))
        return updatedCart
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to add item to cart'
        update(state => ({ ...state, isLoading: false, error: errorMessage }))
        throw error
      }
    },

    // Update cart item quantity
    async updateItem({ lineId, quantity }: UpdateCartItemParams) {
      update(state => ({ ...state, isLoading: true, error: null }))

      try {
        // Get current state synchronously
        let currentState: CartState
        const unsubscribe = subscribe(state => {
          currentState = state
        })
        unsubscribe()

        if (!currentState!.cart) {
          throw new Error('No cart available')
        }

        // Mock updating item quantity
        const updatedItems = currentState!.cart.items?.map(item =>
          item.id === lineId ? { ...item, quantity } : item
        ) || []

        const updatedCart = {
          ...currentState!.cart,
          items: updatedItems,
          subtotal: updatedItems.reduce((sum, item) => sum + ((item.unit_price || 0) * (item.quantity || 0)), 0),
          total: updatedItems.reduce((sum, item) => sum + ((item.unit_price || 0) * (item.quantity || 0)), 0)
        }

        // Save to localStorage
        if (browser) {
          localStorage.setItem(`cart_${updatedCart.id}`, JSON.stringify(updatedCart))
        }

        update(state => ({ ...state, cart: updatedCart, isLoading: false }))
        return updatedCart
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to update cart item'
        update(state => ({ ...state, isLoading: false, error: errorMessage }))
        throw error
      }
    },

    // Remove item from cart
    async removeItem(lineId: string) {
      update(state => ({ ...state, isLoading: true, error: null }))

      try {
        // Get current state synchronously
        let currentState: CartState
        const unsubscribe = subscribe(state => {
          currentState = state
        })
        unsubscribe()

        if (!currentState!.cart) {
          throw new Error('No cart available')
        }

        // Mock removing item from cart
        const updatedItems = currentState!.cart.items?.filter(item => item.id !== lineId) || []

        const updatedCart = {
          ...currentState!.cart,
          items: updatedItems,
          subtotal: updatedItems.reduce((sum, item) => sum + ((item.unit_price || 0) * (item.quantity || 0)), 0),
          total: updatedItems.reduce((sum, item) => sum + ((item.unit_price || 0) * (item.quantity || 0)), 0)
        }

        // Save to localStorage
        if (browser) {
          localStorage.setItem(`cart_${updatedCart.id}`, JSON.stringify(updatedCart))
        }

        update(state => ({ ...state, cart: updatedCart, isLoading: false }))
        return updatedCart
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to remove cart item'
        update(state => ({ ...state, isLoading: false, error: errorMessage }))
        throw error
      }
    },

    // Clear cart
    clear() {
      if (browser) {
        localStorage.removeItem('cart_id')
      }
      set({ cart: null, isLoading: false, error: null })
    },

    // Clear error
    clearError() {
      update(state => ({ ...state, error: null }))
    }
  }
}

export const cartStore = createCartStore()

// Derived stores for convenience
export const cart = derived(cartStore, $cartStore => $cartStore.cart)
export const cartItemsCount = derived(cart, $cart =>
  $cart?.items?.reduce((total, item) => total + (item.quantity || 0), 0) || 0
)
export const cartTotal = derived(cart, $cart => $cart?.total || 0)
export const cartIsLoading = derived(cartStore, $cartStore => $cartStore.isLoading)
export const cartError = derived(cartStore, $cartStore => $cartStore.error)
