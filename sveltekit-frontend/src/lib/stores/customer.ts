import { writable, derived } from 'svelte/store'
import { browser } from '$app/environment'
import type { Customer, CustomerState } from '$lib/types'

// Customer state store
const createCustomerStore = () => {
  const { subscribe, set, update } = writable<CustomerState>({
    customer: null,
    isAuthenticated: false,
    isLoading: false,
    error: null
  })

  return {
    subscribe,

    // Initialize customer (check if authenticated)
    async initialize() {
      update(state => ({ ...state, isLoading: true, error: null }))

      try {
        const token = browser ? localStorage.getItem('auth_token') : null

        if (token) {
          // Mock customer data
          const customer: Customer = {
            id: 'customer_1',
            email: '<EMAIL>',
            first_name: '<PERSON>',
            last_name: '<PERSON><PERSON>',
            phone: '+1234567890'
          }

          set({
            customer,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })

          return customer
        } else {
          set({
            customer: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
          return null
        }
      } catch (error) {
        // Token might be invalid, clear it
        if (browser) {
          localStorage.removeItem('auth_token')
        }

        set({
          customer: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        })
        return null
      }
    },

    // Login
    async login(email: string, password: string) {
      update(state => ({ ...state, isLoading: true, error: null }))

      try {
        // Mock login - simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Mock authentication token
        const token = `mock_token_${Date.now()}`

        if (browser) {
          localStorage.setItem('auth_token', token)
        }

        // Mock customer data
        const customer: Customer = {
          id: 'customer_1',
          email,
          first_name: 'John',
          last_name: 'Doe',
          phone: '+1234567890'
        }

        set({
          customer,
          isAuthenticated: true,
          isLoading: false,
          error: null
        })

        return customer
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Login failed'
        set({
          customer: null,
          isAuthenticated: false,
          isLoading: false,
          error: errorMessage
        })
        throw error
      }
    },

    // Register
    async register(customerData: {
      email: string
      password: string
      first_name: string
      last_name: string
      phone?: string
    }) {
      update(state => ({ ...state, isLoading: true, error: null }))

      try {
        // Mock registration - simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Mock authentication token
        const token = `mock_token_${Date.now()}`

        if (browser) {
          localStorage.setItem('auth_token', token)
        }

        // Mock customer data
        const customer: Customer = {
          id: 'customer_1',
          email: customerData.email,
          first_name: customerData.first_name,
          last_name: customerData.last_name,
          phone: customerData.phone,
        }

        set({
          customer,
          isAuthenticated: true,
          isLoading: false,
          error: null
        })

        return customer
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Registration failed'
        set({
          customer: null,
          isAuthenticated: false,
          isLoading: false,
          error: errorMessage
        })
        throw error
      }
    },

    // Logout
    async logout() {
      update(state => ({ ...state, isLoading: true, error: null }))

      try {
        // Mock logout - just clear local storage
        if (browser) {
          localStorage.removeItem('auth_token')
        }

        set({
          customer: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        })
      } catch (error) {
        // Even if logout fails, clear local state
        if (browser) {
          localStorage.removeItem('auth_token')
        }

        set({
          customer: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        })
      }
    },

    // Update customer
    async updateCustomer(updates: Partial<Customer>) {
      update(state => ({ ...state, isLoading: true, error: null }))

      try {
        // Mock update - simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500))

        // Get current customer and merge updates
        let currentState: CustomerState
        const unsubscribe = subscribe(state => {
          currentState = state
        })
        unsubscribe()

        if (!currentState!.customer) {
          throw new Error('No customer to update')
        }

        const customer = { ...currentState!.customer, ...updates }

        update(state => ({
          ...state,
          customer,
          isLoading: false,
          error: null
        }))

        return customer
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Update failed'
        update(state => ({ ...state, isLoading: false, error: errorMessage }))
        throw error
      }
    },

    // Clear error
    clearError() {
      update(state => ({ ...state, error: null }))
    }
  }
}

export const customerStore = createCustomerStore()

// Derived stores for convenience
export const customer = derived(customerStore, $customerStore => $customerStore.customer)
export const isAuthenticated = derived(customerStore, $customerStore => $customerStore.isAuthenticated)
export const customerIsLoading = derived(customerStore, $customerStore => $customerStore.isLoading)
export const customerError = derived(customerStore, $customerStore => $customerStore.error)
