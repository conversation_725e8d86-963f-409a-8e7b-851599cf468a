import { browser } from '$app/environment'
import { env } from '$env/dynamic/public'

// Defaults to standard port for Medusa server
let MEDUSA_BACKEND_URL = "http://localhost:9000"

// Get environment variables (client-side safe)
const getEnvVar = (key: string) => {
  // Always use public env vars for client-side code
  return env[`PUBLIC_${key}`] || (window as any).__ENV__?.[key]
}

// Set backend URL from environment or default
const envBackendUrl = getEnvVar('MEDUSA_BACKEND_URL')
if (envBackendUrl) {
  MEDUSA_BACKEND_URL = envBackendUrl
} else if (browser && window.location.hostname !== 'localhost') {
  // In production, use the same domain
  MEDUSA_BACKEND_URL = `${window.location.protocol}//${window.location.hostname}:9000`
}

// SDK instance
let sdk: any = null

// Initialize SDK function
export async function initializeSDK() {
  if (sdk) return sdk

  try {
    const { default: Medusa } = await import("@medusajs/js-sdk")

    const publishableKey = getEnvVar('MEDUSA_PUBLISHABLE_KEY')

    sdk = new Medusa({
      baseUrl: MEDUSA_BACKEND_URL,
      debug: getEnvVar('NODE_ENV') === 'development',
      publishableKey: publishableKey,
    })

    console.log('Medusa SDK initialized:', {
      baseUrl: MEDUSA_BACKEND_URL,
      hasPublishableKey: !!publishableKey
    })

    return sdk
  } catch (error) {
    console.error('Failed to initialize Medusa SDK:', error)
    return null
  }
}

// Get SDK instance
export function getSDK() {
  return sdk
}

export const BACKEND_URL = MEDUSA_BACKEND_URL
