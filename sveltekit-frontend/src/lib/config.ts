import { browser } from '$app/environment'
import { env } from '$env/dynamic/public'
import { env as privateEnv } from '$env/dynamic/private'

// Defaults to standard port for Medusa server
let MEDUSA_BACKEND_URL = "http://localhost:9000"

// Get environment variables (works in both client and server)
const getEnvVar = (key: string) => {
  if (browser) {
    return env[`PUBLIC_${key}`] || (window as any).__ENV__?.[key]
  }
  return privateEnv[key] || env[`PUBLIC_${key}`]
}

// Set backend URL from environment or default
const envBackendUrl = getEnvVar('MEDUSA_BACKEND_URL')
if (envBackendUrl) {
  MEDUSA_BACKEND_URL = envBackendUrl
} else if (browser && window.location.hostname !== 'localhost') {
  // In production, use the same domain
  MEDUSA_BACKEND_URL = `${window.location.protocol}//${window.location.hostname}:9000`
}

// SDK instance
let sdk: any = null

// Initialize SDK function
export async function initializeSDK() {
  if (sdk) return sdk

  try {
    const { default: Medusa } = await import("@medusajs/js-sdk")

    const publishableKey = getEnvVar('MEDUSA_PUBLISHABLE_KEY')

    sdk = new Medusa({
      baseUrl: MEDUSA_BACKEND_URL,
      debug: !browser || getEnvVar('NODE_ENV') === 'development',
      publishableKey: publishableKey,
    })

    console.log('Medusa SDK initialized:', {
      baseUrl: MEDUSA_BACKEND_URL,
      hasPublishableKey: !!publishableKey
    })

    return sdk
  } catch (error) {
    console.error('Failed to initialize Medusa SDK:', error)
    return null
  }
}

// Get SDK instance
export function getSDK() {
  return sdk
}

export const BACKEND_URL = MEDUSA_BACKEND_URL
