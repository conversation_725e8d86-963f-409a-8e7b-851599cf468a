import { browser } from '$app/environment'

// Defaults to standard port for Medusa server
let MEDUSA_BACKEND_URL = "http://localhost:9000"

if (browser && window.location.hostname !== 'localhost') {
  // In production, use the same domain
  MEDUSA_BACKEND_URL = `${window.location.protocol}//${window.location.hostname}:9000`
}

// Override with environment variable if available
if (typeof process !== 'undefined' && process.env?.MEDUSA_BACKEND_URL) {
  MEDUSA_BACKEND_URL = process.env.MEDUSA_BACKEND_URL
}

// SDK instance
let sdk: any = null

// Initialize SDK function
export async function initializeSDK() {
  if (!browser || sdk) return sdk

  try {
    const { default: Medusa } = await import("@medusajs/js-sdk")
    sdk = new Medusa({
      baseUrl: MEDUSA_BACKEND_URL,
      debug: true, // Enable debug in development
      publishableKey: process.env.MEDUSA_PUBLISHABLE_KEY,
    })
    return sdk
  } catch (error) {
    console.error('Failed to initialize Medusa SDK:', error)
    return null
  }
}

// Get SDK instance
export function getSDK() {
  return sdk
}

export const BACKEND_URL = MEDUSA_BACKEND_URL
