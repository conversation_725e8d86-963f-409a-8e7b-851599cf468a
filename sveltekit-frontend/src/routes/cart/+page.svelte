<script lang="ts">
  import { cart, cartStore, cartIsLoading, cartError } from '$lib/stores/cart'
  import Button from '$lib/components/ui/Button.svelte'
  import type { CartLineItem } from '$lib/types'
  
  let updatingItems: Set<string> = new Set()
  
  async function updateQuantity(lineId: string, quantity: number) {
    if (quantity < 1) {
      await removeItem(lineId)
      return
    }
    
    updatingItems.add(lineId)
    updatingItems = updatingItems
    
    try {
      await cartStore.updateItem({ lineId, quantity })
    } catch (error) {
      console.error('Failed to update quantity:', error)
    } finally {
      updatingItems.delete(lineId)
      updatingItems = updatingItems
    }
  }
  
  async function removeItem(lineId: string) {
    updatingItems.add(lineId)
    updatingItems = updatingItems
    
    try {
      await cartStore.removeItem(lineId)
    } catch (error) {
      console.error('Failed to remove item:', error)
    } finally {
      updatingItems.delete(lineId)
      updatingItems = updatingItems
    }
  }
  
  function formatPrice(amount: number, currencyCode: string) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode
    }).format(amount / 100)
  }
  
  function getItemPrice(item: CartLineItem) {
    return item.unit_price || 0
  }
  
  function getItemTotal(item: CartLineItem) {
    return (item.unit_price || 0) * (item.quantity || 0)
  }
</script>

<svelte:head>
  <title>Shopping Cart - Jani Vapes</title>
</svelte:head>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <h1 class="text-3xl font-bold text-gray-900 mb-8">Shopping Cart</h1>
  
  {#if $cartIsLoading}
    <div class="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
      <p class="mt-4 text-gray-600">Loading cart...</p>
    </div>
  {:else if $cartError}
    <div class="text-center py-12">
      <p class="text-red-600 mb-4">{$cartError}</p>
      <Button on:click={() => cartStore.clearError()}>
        Dismiss
      </Button>
    </div>
  {:else if !$cart || !$cart.items || $cart.items.length === 0}
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
      </svg>
      <h2 class="mt-4 text-lg font-medium text-gray-900">Your cart is empty</h2>
      <p class="mt-2 text-gray-600">Start shopping to add items to your cart.</p>
      <div class="mt-6">
        <Button href="/products">
          Continue Shopping
        </Button>
      </div>
    </div>
  {:else}
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Cart Items -->
      <div class="lg:col-span-2">
        <div class="space-y-4">
          {#each $cart.items as item (item.id)}
            <div class="card">
              <div class="flex items-center space-x-4">
                <!-- Product Image -->
                <div class="flex-shrink-0 w-20 h-20">
                  {#if item.thumbnail}
                    <img
                      src={item.thumbnail}
                      alt={item.product_title}
                      class="w-full h-full object-cover rounded-md"
                    />
                  {:else}
                    <div class="w-full h-full bg-gray-200 rounded-md flex items-center justify-center">
                      <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  {/if}
                </div>
                
                <!-- Product Details -->
                <div class="flex-1 min-w-0">
                  <h3 class="text-lg font-medium text-gray-900 truncate">
                    {item.product_title}
                  </h3>
                  {#if item.variant_title}
                    <p class="text-sm text-gray-600">{item.variant_title}</p>
                  {/if}
                  <p class="text-lg font-semibold text-gray-900">
                    {formatPrice(getItemPrice(item), $cart.currency_code || 'USD')}
                  </p>
                </div>
                
                <!-- Quantity Controls -->
                <div class="flex items-center space-x-2">
                  <button
                    class="btn btn-outline p-1 h-8 w-8 flex items-center justify-center"
                    disabled={updatingItems.has(item.id) || (item.quantity || 0) <= 1}
                    on:click={() => updateQuantity(item.id, (item.quantity || 0) - 1)}
                  >
                    -
                  </button>
                  <span class="w-8 text-center font-medium">
                    {item.quantity || 0}
                  </span>
                  <button
                    class="btn btn-outline p-1 h-8 w-8 flex items-center justify-center"
                    disabled={updatingItems.has(item.id)}
                    on:click={() => updateQuantity(item.id, (item.quantity || 0) + 1)}
                  >
                    +
                  </button>
                </div>
                
                <!-- Item Total -->
                <div class="text-right">
                  <p class="text-lg font-semibold text-gray-900">
                    {formatPrice(getItemTotal(item), $cart.currency_code || 'USD')}
                  </p>
                  <button
                    class="text-sm text-red-600 hover:text-red-800"
                    disabled={updatingItems.has(item.id)}
                    on:click={() => removeItem(item.id)}
                  >
                    Remove
                  </button>
                </div>
              </div>
            </div>
          {/each}
        </div>
      </div>
      
      <!-- Cart Summary -->
      <div class="lg:col-span-1">
        <div class="card sticky top-4">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>
          
          <div class="space-y-2 mb-4">
            <div class="flex justify-between">
              <span class="text-gray-600">Subtotal</span>
              <span class="font-medium">
                {formatPrice($cart.subtotal || 0, $cart.currency_code || 'USD')}
              </span>
            </div>
            
            {#if $cart.tax_total}
              <div class="flex justify-between">
                <span class="text-gray-600">Tax</span>
                <span class="font-medium">
                  {formatPrice($cart.tax_total, $cart.currency_code || 'USD')}
                </span>
              </div>
            {/if}
            
            {#if $cart.shipping_total}
              <div class="flex justify-between">
                <span class="text-gray-600">Shipping</span>
                <span class="font-medium">
                  {formatPrice($cart.shipping_total, $cart.currency_code || 'USD')}
                </span>
              </div>
            {/if}
          </div>
          
          <div class="border-t pt-4 mb-6">
            <div class="flex justify-between text-lg font-semibold">
              <span>Total</span>
              <span>
                {formatPrice($cart.total || 0, $cart.currency_code || 'USD')}
              </span>
            </div>
          </div>
          
          <Button variant="primary" size="lg" class="w-full mb-4">
            Proceed to Checkout
          </Button>
          
          <Button variant="outline" size="md" class="w-full" href="/products">
            Continue Shopping
          </Button>
        </div>
      </div>
    </div>
  {/if}
</div>
