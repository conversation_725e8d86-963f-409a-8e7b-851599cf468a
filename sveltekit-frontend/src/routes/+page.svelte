<script lang="ts">
  import { onMount } from 'svelte'
  import { ProductsAPI } from '$lib/api/products'
  import { RegionsAPI } from '$lib/api/regions'
  import ProductCard from '$lib/components/ProductCard.svelte'
  import Button from '$lib/components/ui/Button.svelte'
  import type { Product } from '$lib/types'

  let featuredProducts: Product[] = []
  let isLoading = true
  let error: string | null = null
  let regionId: string | null = null

  onMount(async () => {
    try {
      // Get default region
      const regions = await RegionsAPI.listRegions()
      const defaultRegion = regions[0]

      if (defaultRegion) {
        regionId = defaultRegion.id
        // Load featured products
        featuredProducts = await ProductsAPI.getFeaturedProducts(defaultRegion.id, 8)
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load products'
    } finally {
      isLoading = false
    }
  })

  function handleAddedToCart(event: CustomEvent) {
    const { product } = event.detail
    // Show success message or notification
    console.log('Added to cart:', product.title)
  }
</script>

<svelte:head>
  <title>Jani Vapes - Premium Vaping Products</title>
  <meta name="description" content="Discover premium vaping products at Jani Vapes. Quality devices, e-liquids, and accessories for the ultimate vaping experience." />
</svelte:head>

<!-- Hero Section -->
<section class="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
    <div class="text-center">
      <h1 class="text-4xl md:text-6xl font-bold mb-6">
        Premium Vaping Experience
      </h1>
      <p class="text-xl md:text-2xl mb-8 text-primary-100">
        Discover our curated collection of high-quality vaping products
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <Button variant="secondary" size="lg" class="bg-white text-primary-600 hover:bg-gray-100">
          Shop Now
        </Button>
        <Button variant="outline" size="lg" class="border-white text-white hover:bg-white hover:text-primary-600">
          Learn More
        </Button>
      </div>
    </div>
  </div>
</section>

<!-- Featured Products Section -->
<section class="py-16">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
        Featured Products
      </h2>
      <p class="text-lg text-gray-600">
        Handpicked products for the best vaping experience
      </p>
    </div>

    {#if isLoading}
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {#each Array(8) as _}
          <div class="card animate-pulse">
            <div class="aspect-square bg-gray-200 rounded-lg mb-4"></div>
            <div class="space-y-2">
              <div class="h-4 bg-gray-200 rounded"></div>
              <div class="h-4 bg-gray-200 rounded w-3/4"></div>
              <div class="h-8 bg-gray-200 rounded"></div>
            </div>
          </div>
        {/each}
      </div>
    {:else if error}
      <div class="text-center py-12">
        <p class="text-red-600 mb-4">{error}</p>
        <Button on:click={() => window.location.reload()}>
          Try Again
        </Button>
      </div>
    {:else if featuredProducts.length > 0 && regionId}
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {#each featuredProducts as product}
          <ProductCard
            {product}
            on:added-to-cart={handleAddedToCart}
          />
        {/each}
      </div>

      <div class="text-center mt-12">
        <Button variant="outline" size="lg">
          View All Products
        </Button>
      </div>
    {:else}
      <div class="text-center py-12">
        <p class="text-gray-600">No products available at the moment.</p>
      </div>
    {/if}
  </div>
</section>

<!-- Features Section -->
<section class="bg-gray-100 py-16">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
        Why Choose Jani Vapes?
      </h2>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div class="text-center">
        <div class="bg-primary-600 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
          <svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Premium Quality</h3>
        <p class="text-gray-600">Only the finest products from trusted manufacturers</p>
      </div>

      <div class="text-center">
        <div class="bg-primary-600 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
          <svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Fast Shipping</h3>
        <p class="text-gray-600">Quick and secure delivery to your doorstep</p>
      </div>

      <div class="text-center">
        <div class="bg-primary-600 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
          <svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z" />
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Expert Support</h3>
        <p class="text-gray-600">Knowledgeable team ready to help you</p>
      </div>
    </div>
  </div>
</section>