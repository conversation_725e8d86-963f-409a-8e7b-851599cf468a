<script lang="ts">
  import { env } from '$env/dynamic/public'
  
  // Get all environment variables
  const allEnvVars = { ...env }
  const importMetaEnv = { ...import.meta.env }
</script>

<svelte:head>
  <title>Environment Debug - Jani Vapes</title>
</svelte:head>

<div class="max-w-4xl mx-auto px-4 py-8">
  <h1 class="text-3xl font-bold mb-6">Environment Variables Debug</h1>
  
  <div class="space-y-6">
    <div class="bg-blue-50 p-4 rounded-lg">
      <h2 class="text-xl font-semibold text-blue-900 mb-4">Using $env/dynamic/public</h2>
      <div class="space-y-2 text-sm">
        <p><strong>PUBLIC_MEDUSA_BACKEND_URL:</strong> {env.PUBLIC_MEDUSA_BACKEND_URL || 'Not set'}</p>
        <p><strong>PUBLIC_MEDUSA_PUBLISHABLE_KEY:</strong> {env.PUBLIC_MEDUSA_PUBLISHABLE_KEY ? '✅ Set (' + env.PUBLIC_MEDUSA_PUBLISHABLE_KEY.substring(0, 10) + '...)' : '❌ Not set'}</p>
        <p><strong>PUBLIC_NODE_ENV:</strong> {env.PUBLIC_NODE_ENV || 'Not set'}</p>
      </div>
    </div>

    <div class="bg-green-50 p-4 rounded-lg">
      <h2 class="text-xl font-semibold text-green-900 mb-4">Using import.meta.env</h2>
      <div class="space-y-2 text-sm">
        <p><strong>PUBLIC_MEDUSA_BACKEND_URL:</strong> {import.meta.env.PUBLIC_MEDUSA_BACKEND_URL || 'Not set'}</p>
        <p><strong>PUBLIC_MEDUSA_PUBLISHABLE_KEY:</strong> {import.meta.env.PUBLIC_MEDUSA_PUBLISHABLE_KEY ? '✅ Set (' + import.meta.env.PUBLIC_MEDUSA_PUBLISHABLE_KEY.substring(0, 10) + '...)' : '❌ Not set'}</p>
        <p><strong>PUBLIC_NODE_ENV:</strong> {import.meta.env.PUBLIC_NODE_ENV || 'Not set'}</p>
        <p><strong>MODE:</strong> {import.meta.env.MODE || 'Not set'}</p>
        <p><strong>DEV:</strong> {import.meta.env.DEV ? 'true' : 'false'}</p>
        <p><strong>PROD:</strong> {import.meta.env.PROD ? 'true' : 'false'}</p>
      </div>
    </div>

    <div class="bg-gray-50 p-4 rounded-lg">
      <h2 class="text-xl font-semibold text-gray-900 mb-4">All $env/dynamic/public variables</h2>
      <pre class="text-xs bg-white p-2 rounded border overflow-auto">{JSON.stringify(allEnvVars, null, 2)}</pre>
    </div>

    <div class="bg-gray-50 p-4 rounded-lg">
      <h2 class="text-xl font-semibold text-gray-900 mb-4">All import.meta.env variables</h2>
      <pre class="text-xs bg-white p-2 rounded border overflow-auto">{JSON.stringify(importMetaEnv, null, 2)}</pre>
    </div>
  </div>
</div>
