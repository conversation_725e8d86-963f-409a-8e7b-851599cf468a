<script lang="ts">
  import { onMount } from 'svelte'
  import { cartStore } from '$lib/stores/cart'
  import ProductInfo from '$lib/components/ProductInfo.svelte'
  import ImageGallery from '$lib/components/ImageGallery.svelte'
  import ProductActions from '$lib/components/ProductActions.svelte'
  import ProductTabs from '$lib/components/ProductTabs.svelte'
  import type { PageData } from './$types'

  export let data: PageData

  $: ({ product, region, countryCode } = data)

  onMount(async () => {
    // Initialize cart with the current region
    try {
      await cartStore.initialize(region.id)
    } catch (error) {
      console.error('Failed to initialize cart:', error)
    }
  })
</script>

<svelte:head>
  <title>{product.title} | Vape Store</title>
  <meta name="description" content={product.description || product.title} />
  <meta property="og:title" content="{product.title} | Vape Store" />
  <meta property="og:description" content={product.description || product.title} />
  {#if product.thumbnail}
    <meta property="og:image" content={product.thumbnail} />
  {/if}
</svelte:head>

<div class="min-h-screen bg-white">
  <!-- Product Container -->
  <div 
    class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"
    data-testid="product-container"
  >
    <div class="flex flex-col lg:flex-row lg:items-start gap-8 lg:gap-12">
      <!-- Left Column: Product Info and Tabs -->
      <div class="flex flex-col lg:sticky lg:top-48 lg:max-w-[300px] w-full gap-y-6">
        <ProductInfo {product} />
        <ProductTabs {product} />
      </div>

      <!-- Center Column: Image Gallery -->
      <div class="flex-1 lg:max-w-2xl">
        <ImageGallery {product} />
      </div>

      <!-- Right Column: Product Actions -->
      <div class="flex flex-col lg:sticky lg:top-48 lg:max-w-[300px] w-full gap-y-6">
        <ProductActions {product} {region} />
        
        <!-- Additional Product Info -->
        <div class="border-t pt-6">
          <div class="space-y-4 text-sm text-gray-600">
            <div class="flex items-center gap-2">
              <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Free shipping on orders over $50</span>
            </div>
            <div class="flex items-center gap-2">
              <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>30-day return policy</span>
            </div>
            <div class="flex items-center gap-2">
              <svg class="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              <span>Secure checkout</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Related Products Section (placeholder for future implementation) -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
    <div class="border-t pt-16">
      <h2 class="text-2xl font-bold text-gray-900 mb-8">Related Products</h2>
      <div class="text-gray-500 text-center py-8">
        Related products will be displayed here in a future update.
      </div>
    </div>
  </div>
</div>
