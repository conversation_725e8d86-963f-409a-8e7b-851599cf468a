<script lang="ts">
  import { onMount } from 'svelte'
  import { testMedusaConnection, testRegionsOnly, testProductsOnly } from '$lib/test-medusa'
  import Button from '$lib/components/ui/Button.svelte'

  let testResults: string[] = []
  let isRunning = false
  let connectionStatus: 'unknown' | 'success' | 'error' = 'unknown'

  function addLog(message: string) {
    testResults = [...testResults, `${new Date().toLocaleTimeString()}: ${message}`]
  }

  async function runFullTest() {
    isRunning = true
    testResults = []
    connectionStatus = 'unknown'
    
    addLog('🚀 Starting Medusa API tests...')
    
    try {
      const success = await testMedusaConnection()
      connectionStatus = success ? 'success' : 'error'
      addLog(success ? '✅ All tests completed successfully!' : '❌ Some tests failed')
    } catch (error) {
      connectionStatus = 'error'
      addLog(`❌ Test suite failed: ${error}`)
    } finally {
      isRunning = false
    }
  }

  async function testRegions() {
    isRunning = true
    addLog('📍 Testing regions only...')
    
    try {
      const regions = await testRegionsOnly()
      addLog(`✅ Regions test passed: ${regions.length} regions found`)
    } catch (error) {
      addLog(`❌ Regions test failed: ${error}`)
    } finally {
      isRunning = false
    }
  }

  async function testProducts() {
    isRunning = true
    addLog('📦 Testing products only...')
    
    try {
      // First get a region
      const regions = await testRegionsOnly()
      if (regions.length > 0) {
        const products = await testProductsOnly(regions[0].id)
        addLog(`✅ Products test passed: ${products.count} products found`)
      } else {
        addLog('❌ No regions available for products test')
      }
    } catch (error) {
      addLog(`❌ Products test failed: ${error}`)
    } finally {
      isRunning = false
    }
  }

  function clearLogs() {
    testResults = []
    connectionStatus = 'unknown'
  }
</script>

<svelte:head>
  <title>API Test - Jani Vapes</title>
</svelte:head>

<div class="max-w-4xl mx-auto px-4 py-8">
  <h1 class="text-3xl font-bold mb-6">Medusa API Integration Test</h1>
  
  <div class="mb-6">
    <p class="text-gray-600 mb-4">
      Use this page to test the connection between your SvelteKit frontend and Medusa backend.
    </p>
    
    <div class="flex flex-wrap gap-4 mb-4">
      <Button 
        on:click={runFullTest} 
        disabled={isRunning}
        class="bg-blue-600 hover:bg-blue-700"
      >
        {isRunning ? 'Running...' : 'Run Full Test'}
      </Button>
      
      <Button 
        on:click={testRegions} 
        disabled={isRunning}
        variant="outline"
      >
        Test Regions Only
      </Button>
      
      <Button 
        on:click={testProducts} 
        disabled={isRunning}
        variant="outline"
      >
        Test Products Only
      </Button>
      
      <Button 
        on:click={clearLogs} 
        disabled={isRunning}
        variant="outline"
      >
        Clear Logs
      </Button>
    </div>

    {#if connectionStatus !== 'unknown'}
      <div class="p-4 rounded-lg mb-4 {connectionStatus === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
        {#if connectionStatus === 'success'}
          ✅ Connection successful! Your Medusa integration is working.
        {:else}
          ❌ Connection failed. Check the logs below and your configuration.
        {/if}
      </div>
    {/if}
  </div>

  <div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
    <div class="flex justify-between items-center mb-2">
      <h3 class="text-white font-semibold">Test Logs</h3>
      {#if isRunning}
        <div class="text-yellow-400">Running...</div>
      {/if}
    </div>
    
    <div class="max-h-96 overflow-y-auto">
      {#if testResults.length === 0}
        <div class="text-gray-500">No logs yet. Click a test button to start.</div>
      {:else}
        {#each testResults as result}
          <div class="mb-1">{result}</div>
        {/each}
      {/if}
    </div>
  </div>

  <div class="mt-8 p-4 bg-blue-50 rounded-lg">
    <h3 class="font-semibold text-blue-900 mb-2">Configuration Check</h3>
    <div class="text-sm text-blue-800">
      <p><strong>Backend URL:</strong> {import.meta.env.PUBLIC_MEDUSA_BACKEND_URL || 'http://localhost:9000'}</p>
      <p><strong>Publishable Key:</strong> {import.meta.env.PUBLIC_MEDUSA_PUBLISHABLE_KEY ? '✅ Set' : '❌ Not set'}</p>
    </div>
  </div>

  <div class="mt-8 p-4 bg-yellow-50 rounded-lg">
    <h3 class="font-semibold text-yellow-900 mb-2">Troubleshooting</h3>
    <div class="text-sm text-yellow-800 space-y-2">
      <p>If tests are failing, check:</p>
      <ul class="list-disc list-inside space-y-1">
        <li>Medusa backend is running on the configured URL</li>
        <li>Environment variables are set correctly in .env.local</li>
        <li>CORS is configured properly in your Medusa backend</li>
        <li>Publishable key is valid (if required)</li>
        <li>Your Medusa backend has regions and products configured</li>
      </ul>
    </div>
  </div>
</div>
