<script lang="ts">
  import { onMount } from 'svelte'
  import { ProductsAPI } from '$lib/api/products'
  import { RegionsAPI } from '$lib/api/regions'
  import ProductCard from '$lib/components/ProductCard.svelte'
  import Button from '$lib/components/ui/Button.svelte'
  import Input from '$lib/components/ui/Input.svelte'
  import type { Product } from '$lib/types'

  let products: Product[] = []
  let isLoading = true
  let error: string | null = null
  let regionId: string | null = null
  let searchQuery = ''
  let currentPage = 1
  let totalCount = 0
  let hasNextPage = false

  const PRODUCTS_PER_PAGE = 12

  onMount(async () => {
    await loadInitialData()
  })

  async function loadInitialData() {
    try {
      // Get default region
      const regions = await RegionsAPI.listRegions()
      const defaultRegion = regions[0]

      if (defaultRegion) {
        regionId = defaultRegion.id
        await loadProducts()
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load products'
      isLoading = false
    }
  }

  async function loadProducts() {
    if (!regionId) return

    isLoading = true
    error = null

    try {
      const filters = searchQuery ? { q: searchQuery } : {}
      const pagination = {
        limit: PRODUCTS_PER_PAGE,
        offset: (currentPage - 1) * PRODUCTS_PER_PAGE
      }

      const response = await ProductsAPI.listProducts(regionId, filters, pagination)
      products = response.products
      totalCount = response.count
      hasNextPage = response.nextPage !== null
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load products'
    } finally {
      isLoading = false
    }
  }

  async function handleSearch() {
    currentPage = 1
    await loadProducts()
  }

  async function handleNextPage() {
    if (hasNextPage) {
      currentPage++
      await loadProducts()
    }
  }

  async function handlePrevPage() {
    if (currentPage > 1) {
      currentPage--
      await loadProducts()
    }
  }

  function handleAddedToCart(event: CustomEvent) {
    const { product } = event.detail
    // Show success message or notification
    console.log('Added to cart:', product.title)
  }
</script>

<svelte:head>
  <title>Products - Jani Vapes</title>
  <meta name="description" content="Browse our complete collection of premium vaping products." />
</svelte:head>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Page Header -->
  <div class="mb-8">
    <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
      All Products
    </h1>
    <p class="text-lg text-gray-600">
      Discover our complete collection of premium vaping products
    </p>
  </div>

  <!-- Search and Filters -->
  <div class="mb-8">
    <div class="flex flex-col sm:flex-row gap-4">
      <div class="flex-1">
        <Input
          type="search"
          placeholder="Search products..."
          bind:value={searchQuery}
          on:input={handleSearch}
        />
      </div>
      <Button on:click={handleSearch}>
        Search
      </Button>
    </div>
  </div>

  <!-- Products Grid -->
  {#if isLoading}
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {#each Array(PRODUCTS_PER_PAGE) as _}
        <div class="card animate-pulse">
          <div class="aspect-square bg-gray-200 rounded-lg mb-4"></div>
          <div class="space-y-2">
            <div class="h-4 bg-gray-200 rounded"></div>
            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
            <div class="h-8 bg-gray-200 rounded"></div>
          </div>
        </div>
      {/each}
    </div>
  {:else if error}
    <div class="text-center py-12">
      <p class="text-red-600 mb-4">{error}</p>
      <Button on:click={loadProducts}>
        Try Again
      </Button>
    </div>
  {:else if products.length > 0 && regionId}
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {#each products as product}
        <ProductCard
          {product}
          on:added-to-cart={handleAddedToCart}
        />
      {/each}
    </div>

    <!-- Pagination -->
    <div class="flex items-center justify-between">
      <p class="text-sm text-gray-700">
        Showing {(currentPage - 1) * PRODUCTS_PER_PAGE + 1} to {Math.min(currentPage * PRODUCTS_PER_PAGE, totalCount)} of {totalCount} products
      </p>

      <div class="flex space-x-2">
        <Button
          variant="outline"
          disabled={currentPage === 1}
          on:click={handlePrevPage}
        >
          Previous
        </Button>
        <Button
          variant="outline"
          disabled={!hasNextPage}
          on:click={handleNextPage}
        >
          Next
        </Button>
      </div>
    </div>
  {:else}
    <div class="text-center py-12">
      <p class="text-gray-600">No products found.</p>
      {#if searchQuery}
        <Button class="mt-4" on:click={() => { searchQuery = ''; handleSearch(); }}>
          Clear Search
        </Button>
      {/if}
    </div>
  {/if}
</div>
